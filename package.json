{"name": "rohit-bot", "productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "description": "Simplified Electron app with Vite", "author": {"name": "<PERSON><PERSON><PERSON>"}, "main": "dist-electron/main.js", "scripts": {"dev": "vite", "start": "cross-env NODE_ENV=production electron .", "start:dev": "cross-env NODE_ENV=development electron .", "start:prod": "npm run build && cross-env NODE_ENV=production electron .", "build": "vite build", "build:dir": "vite build && electron-builder --dir", "build:prod": "vite build && electron-builder", "preview": "vite preview", "dist": "electron-builder", "dist:win": "electron-builder --win", "dist:linux": "electron-builder --linux", "dist:mac": "electron-builder --mac"}, "build": {"appId": "com.rohit-bot.app", "files": ["dist/**/*", "dist-electron/**/*", "assets/**/*", "public/**/*"], "directories": {"output": "."}, "win": {"target": "portable", "icon": "assets/icons/logo.ico", "requestedExecutionLevel": "asInvoker"}, "icon": "assets/icons/logo.ico", "asar": true}, "dependencies": {"@google/genai": "^0.14.0", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@langchain/anthropic": "^0.3.20", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.55", "@langchain/google-genai": "^0.2.9", "@langchain/groq": "^0.2.2", "@langchain/ollama": "^0.2.0", "@langchain/openai": "^0.5.10", "electron-squirrel-startup": "^1.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0"}, "devDependencies": {"@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "cross-env": "^7.0.3", "electron": "^26.6.10", "electron-builder": "^24.13.3", "typescript": "^5.4.5", "vite": "^4.5.14", "vite-plugin-electron": "^0.14.1"}}