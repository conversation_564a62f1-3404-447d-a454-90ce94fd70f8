// Enhanced test script to verify Ollama integration
const { testOllamaConnection, fetchOllamaModels } = require('./src/utils/langchainConfig.ts');

async function testEnhancedOllamaIntegration() {
  console.log('🧪 Testing Enhanced Ollama Integration...\n');
  
  try {
    console.log('1. Testing Ollama connection status...');
    const status = await testOllamaConnection();
    
    if (status.isConnected) {
      console.log('✅ Ollama connection successful');
      console.log(`📊 Found ${status.modelsCount} models`);
    } else {
      console.log('❌ Ollama connection failed');
      console.log(`🔍 Error: ${status.error}`);
      return;
    }
    
    console.log('\n2. Testing model fetching...');
    const models = await fetchOllamaModels();
    
    if (models.length > 0) {
      console.log(`✅ Successfully fetched ${models.length} Ollama models:`);
      models.forEach(model => {
        console.log(`   - ${model.name} (${model.description})`);
      });
    } else {
      console.log('⚠️  No Ollama models found');
      console.log('💡 Install models using: ollama pull llama2');
    }
    
    console.log('\n3. Testing error handling...');
    // Test with invalid URL
    const originalEnv = process.env.VITE_OLLAMA_BASE_URL;
    process.env.VITE_OLLAMA_BASE_URL = 'http://localhost:99999';
    
    try {
      await testOllamaConnection();
      console.log('❌ Error handling test failed - should have thrown error');
    } catch (error) {
      console.log('✅ Error handling works correctly');
      console.log(`   Error caught: ${error.message}`);
    }
    
    // Restore original URL
    if (originalEnv) {
      process.env.VITE_OLLAMA_BASE_URL = originalEnv;
    } else {
      delete process.env.VITE_OLLAMA_BASE_URL;
    }
    
    console.log('\n🎉 Enhanced Ollama integration test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testEnhancedOllamaIntegration();
