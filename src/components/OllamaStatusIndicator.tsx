import React, { useState, useEffect } from 'react';
import { testOllamaConnection, type OllamaStatus } from '../utils/langchainConfig';
import styles from './OllamaStatusIndicator.module.css';

interface OllamaStatusIndicatorProps {
  onStatusChange?: (status: OllamaStatus) => void;
  showDetails?: boolean;
  className?: string;
}

const OllamaStatusIndicator: React.FC<OllamaStatusIndicatorProps> = ({
  onStatusChange,
  showDetails = false,
  className = ''
}) => {
  const [status, setStatus] = useState<OllamaStatus>({
    isConnected: false,
    modelsCount: 0
  });
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      const newStatus = await testOllamaConnection();
      setStatus(newStatus);
      setLastChecked(new Date());
      onStatusChange?.(newStatus);
    } catch (error) {
      console.error('Error checking Ollama connection:', error);
      const errorStatus: OllamaStatus = {
        isConnected: false,
        error: 'Connection check failed',
        modelsCount: 0
      };
      setStatus(errorStatus);
      onStatusChange?.(errorStatus);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
    
    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = () => {
    if (isChecking) return '⏳';
    return status.isConnected ? '🟢' : '🔴';
  };

  const getStatusText = () => {
    if (isChecking) return 'Checking...';
    if (status.isConnected) {
      return `Connected (${status.modelsCount} models)`;
    }
    return status.error || 'Disconnected';
  };

  const getStatusClass = () => {
    if (isChecking) return styles.checking;
    return status.isConnected ? styles.connected : styles.disconnected;
  };

  return (
    <div className={`${styles.statusIndicator} ${getStatusClass()} ${className}`}>
      <div className={styles.statusIcon}>
        {getStatusIcon()}
      </div>
      
      {showDetails && (
        <div className={styles.statusDetails}>
          <div className={styles.statusText}>
            {getStatusText()}
          </div>
          
          {lastChecked && (
            <div className={styles.lastChecked}>
              Last checked: {lastChecked.toLocaleTimeString()}
            </div>
          )}
          
          {!status.isConnected && status.error && (
            <div className={styles.errorMessage}>
              {status.error}
            </div>
          )}
          
          <button 
            className={styles.refreshButton}
            onClick={checkConnection}
            disabled={isChecking}
            title="Refresh connection status"
          >
            {isChecking ? '⏳' : '🔄'}
          </button>
        </div>
      )}
    </div>
  );
};

export default OllamaStatusIndicator;
