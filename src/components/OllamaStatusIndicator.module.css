.statusIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.statusIcon {
  font-size: 14px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statusDetails {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.statusText {
  font-weight: 500;
  color: var(--text-primary, #333);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.lastChecked {
  font-size: 10px;
  color: var(--text-secondary, #666);
  opacity: 0.8;
  line-height: 1.2;
}

.errorMessage {
  font-size: 10px;
  color: var(--error-color, #dc3545);
  font-weight: 500;
  max-width: 200px;
  word-wrap: break-word;
  line-height: 1.3;
  margin-top: 2px;
}

.refreshButton {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  align-self: flex-start;
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
}

.refreshButton:hover:not(:disabled) {
  background-color: var(--background-hover, #f5f5f5);
  transform: scale(1.1);
}

.refreshButton:active:not(:disabled) {
  transform: scale(0.95);
}

.refreshButton:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Status-specific styles */
.connected {
  background-color: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.connected .statusText {
  color: var(--success-color, #059669);
}

.connected .statusIcon {
  color: var(--success-color, #059669);
}

.disconnected {
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.disconnected .statusText {
  color: var(--error-color, #dc3545);
}

.disconnected .statusIcon {
  color: var(--error-color, #dc3545);
}

.checking {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.checking .statusText {
  color: var(--info-color, #3b82f6);
}

.checking .statusIcon {
  color: var(--info-color, #3b82f6);
}

/* Compact version for inline use */
.statusIndicator.compact {
  padding: 2px 6px;
  border-radius: 4px;
  gap: 4px;
}

.statusIndicator.compact .statusIcon {
  font-size: 12px;
}

.statusIndicator.compact .statusText {
  font-size: 11px;
}

/* Animation for checking state */
.checking .statusIcon {
  animation: pulse 1.5s ease-in-out infinite;
}

.checking .refreshButton {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .statusIndicator {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .statusIcon {
    font-size: 12px;
  }
  
  .errorMessage {
    font-size: 9px;
    max-width: 150px;
  }
  
  .refreshButton {
    font-size: 10px;
    min-width: 18px;
    height: 18px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .connected {
    background-color: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.3);
  }
  
  .disconnected {
    background-color: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
  }
  
  .checking {
    background-color: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
  }
  
  .refreshButton:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
