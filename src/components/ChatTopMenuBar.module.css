.menu-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  z-index: 2000;
  background: var(--background-secondary, #fff);
  border-bottom: 1px solid var(--border-color, #eee);
  display: flex;
  align-items: center;
  height: 48px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.toggle-button {
  margin-left: 16px;
  margin-right: 12px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-secondary, #666);
}

.home-button {
  margin-left: 24px;
  background: none;
  border: 1px solid var(--border-color, #eee);
  border-radius: 6px;
  font-size: 14px;
  padding: 4px 12px;
  cursor: pointer;
  color: var(--text-secondary, #666);
  background-color: var(--background-primary, #fafafa);
}

.settings-button {
  margin-right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary, #666);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.icon {
  width: 20px;
  height: 20px;
}

.model-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #eee);
  border-radius: 6px;
  background: var(--background-primary, #fafafa);
  color: var(--text-primary, #333);
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  min-width: 160px;
  height: 32px;
}

.dropdown-button svg {
  width: 16px;
  height: 16px;
  color: var(--text-secondary, #666);
  margin-left: 4px;
  flex-shrink: 0;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  background: var(--background-secondary, #fff);
  border: 1px solid var(--border-color, #eee);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-primary, #333);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.dropdown-item:hover {
  background: var(--background-hover, #f5f5f5);
}

.dropdown-item.selected {
  background: var(--background-selected, #eef2ff);
  color: var(--text-selected, #4f46e5);
}

/* Ollama-specific styles */
.inline-status {
  margin-left: 4px;
  margin-right: 4px;
}

.provider-status {
  margin-left: auto;
  font-size: 12px;
}

.error-message {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: var(--error-color, #dc3545);
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 4px;
  margin: 4px;
}

.error-message span {
  font-weight: 500;
  font-size: 13px;
}

.error-message small {
  font-size: 11px;
  color: var(--text-secondary, #666);
  font-family: 'Courier New', monospace;
}

.dropdown-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropdown-button:disabled svg {
  opacity: 0.5;
}

.spacer {
  flex: 1;
}