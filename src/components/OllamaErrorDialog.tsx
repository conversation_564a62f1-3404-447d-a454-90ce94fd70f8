import React from 'react';
import styles from './OllamaErrorDialog.module.css';

interface OllamaErrorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  error: string;
  onRetry?: () => void;
}

const OllamaErrorDialog: React.FC<OllamaErrorDialogProps> = ({
  isOpen,
  onClose,
  error,
  onRetry
}) => {
  if (!isOpen) return null;

  const isConnectionError = error.includes('unavailable') || error.includes('Service not running');
  const isModelError = error.includes('not available') || error.includes('Install with:');

  const getErrorIcon = () => {
    if (isConnectionError) return '🔌';
    if (isModelError) return '📦';
    return '⚠️';
  };

  const getErrorTitle = () => {
    if (isConnectionError) return 'Ollama Service Not Running';
    if (isModelError) return 'Model Not Available';
    return 'Ollama Error';
  };

  const getErrorDescription = () => {
    if (isConnectionError) {
      return 'The Ollama service is not running on your machine. Please start Ollama to use local AI models.';
    }
    if (isModelError) {
      return 'The selected model is not installed in Ollama. You can install it using the command below.';
    }
    return 'There was an issue with Ollama. Please check the error details below.';
  };

  const getSolutionSteps = () => {
    if (isConnectionError) {
      return [
        'Download and install Ollama from ollama.ai',
        'Start the Ollama service',
        'Install at least one model (e.g., ollama pull llama2)',
        'Refresh this page or click Retry'
      ];
    }
    if (isModelError) {
      const modelMatch = error.match(/ollama pull (\w+)/);
      const modelName = modelMatch ? modelMatch[1] : 'llama2';
      return [
        `Open terminal/command prompt`,
        `Run: ollama pull ${modelName}`,
        'Wait for the model to download',
        'Click Retry to refresh available models'
      ];
    }
    return [
      'Check that Ollama is running',
      'Verify your internet connection',
      'Try restarting Ollama',
      'Contact support if the issue persists'
    ];
  };

  return (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.dialog} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <div className={styles.icon}>{getErrorIcon()}</div>
          <h2 className={styles.title}>{getErrorTitle()}</h2>
          <button className={styles.closeButton} onClick={onClose}>×</button>
        </div>

        <div className={styles.content}>
          <p className={styles.description}>{getErrorDescription()}</p>
          
          <div className={styles.errorDetails}>
            <strong>Error Details:</strong>
            <code className={styles.errorCode}>{error}</code>
          </div>

          <div className={styles.solution}>
            <strong>How to fix this:</strong>
            <ol className={styles.steps}>
              {getSolutionSteps().map((step, index) => (
                <li key={index}>{step}</li>
              ))}
            </ol>
          </div>

          {isConnectionError && (
            <div className={styles.helpLinks}>
              <a 
                href="https://ollama.ai/download" 
                target="_blank" 
                rel="noopener noreferrer"
                className={styles.helpLink}
              >
                📥 Download Ollama
              </a>
              <a 
                href="https://github.com/ollama/ollama#quickstart" 
                target="_blank" 
                rel="noopener noreferrer"
                className={styles.helpLink}
              >
                📖 Quick Start Guide
              </a>
            </div>
          )}
        </div>

        <div className={styles.actions}>
          <button className={styles.secondaryButton} onClick={onClose}>
            Close
          </button>
          {onRetry && (
            <button className={styles.primaryButton} onClick={onRetry}>
              Retry Connection
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default OllamaErrorDialog;
