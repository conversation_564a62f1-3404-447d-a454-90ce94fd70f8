.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.dialog {
  background: var(--background-secondary, #fff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 24px 16px;
  border-bottom: 1px solid var(--border-color, #eee);
}

.icon {
  font-size: 24px;
  flex-shrink: 0;
}

.title {
  flex: 1;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary, #666);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.closeButton:hover {
  background-color: var(--background-hover, #f5f5f5);
  color: var(--text-primary, #333);
}

.content {
  padding: 16px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.description {
  margin: 0;
  color: var(--text-primary, #333);
  line-height: 1.5;
  font-size: 14px;
}

.errorDetails {
  background-color: var(--background-tertiary, #f8f9fa);
  border: 1px solid var(--border-color, #eee);
  border-radius: 6px;
  padding: 12px;
  font-size: 13px;
}

.errorDetails strong {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary, #333);
}

.errorCode {
  display: block;
  background-color: var(--background-primary, #fff);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--error-color, #dc3545);
  word-break: break-word;
  white-space: pre-wrap;
}

.solution {
  font-size: 14px;
}

.solution strong {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary, #333);
}

.steps {
  margin: 0;
  padding-left: 20px;
  color: var(--text-primary, #333);
}

.steps li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.steps li:last-child {
  margin-bottom: 0;
}

.helpLinks {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.helpLink {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: var(--background-tertiary, #f8f9fa);
  border: 1px solid var(--border-color, #eee);
  border-radius: 6px;
  text-decoration: none;
  color: var(--text-primary, #333);
  font-size: 13px;
  transition: all 0.2s ease;
}

.helpLink:hover {
  background-color: var(--background-hover, #e9ecef);
  border-color: var(--border-hover, #ddd);
  text-decoration: none;
}

.actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-color, #eee);
}

.primaryButton {
  background-color: var(--accent-coral, #ff6b6b);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primaryButton:hover {
  background-color: var(--accent-coral-dark, #e55a4f);
  transform: translateY(-1px);
}

.secondaryButton {
  background-color: var(--background-tertiary, #f8f9fa);
  color: var(--text-primary, #333);
  border: 1px solid var(--border-color, #eee);
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondaryButton:hover {
  background-color: var(--background-hover, #e9ecef);
  border-color: var(--border-hover, #ddd);
}

/* Responsive design */
@media (max-width: 768px) {
  .overlay {
    padding: 10px;
  }
  
  .dialog {
    max-height: 95vh;
  }
  
  .header {
    padding: 20px 20px 12px;
  }
  
  .content {
    padding: 12px 20px 20px;
  }
  
  .actions {
    padding: 12px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    justify-content: center;
  }
  
  .helpLinks {
    flex-direction: column;
  }
  
  .helpLink {
    justify-content: center;
  }
}
