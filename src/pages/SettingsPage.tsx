import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { AVAILABLE_MODELS, getModelForProvider, testOllamaConnection, type OllamaStatus } from '../utils/langchainConfig';
import { getMemoryLimit, setMemoryLimit, getMemoryImpact } from '../utils/memoryUtils';
import OllamaStatusIndicator from '../components/OllamaStatusIndicator';
import styles from './SettingsPage.module.css';

interface ProviderConfig {
  name: string;
  description: string;
  docsUrl: string;
}

// Add webkitdirectory to HTMLInputElement
declare module 'react' {
  interface HTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    webkitdirectory?: string;
  }
}

const PROVIDERS: Record<string, ProviderConfig> = {
    groq: {
        name: "Groq",
        description: "Deepseek...",
        docsUrl: "https://console.groq.com/keys"
      },
  gemini: {
    name: "Google Gemini",
    description: "Gemini Pro...",
    docsUrl: "https://makersuite.google.com/app/apikey"
  },
  openai: {
    name: "OpenAI",
    description: "GPT 4.1...",
    docsUrl: "https://platform.openai.com/api-keys"
  },
  anthropic: {
    name: "Anthropic",
    description: "Sonnet 3.7...",
    docsUrl: "https://console.anthropic.com/settings/keys"
  },
  ollama: {
    name: "Ollama",
    description: "Local AI models running on your machine. No API key required.",
    docsUrl: "https://ollama.ai/download"
  }
};

const SettingsPage: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);
  const [chatMemory, setChatMemory] = useState<number>(5);
  const [ollamaStatus, setOllamaStatus] = useState<OllamaStatus>({
    isConnected: false,
    modelsCount: 0
  });
  const [isTestingOllama, setIsTestingOllama] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('rohit_settings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      setApiKeys(settings.apiKeys || {});
      setSelectedFolders(settings.folders || []);
    }

    // Load memory limit using utility function
    setChatMemory(getMemoryLimit());
  }, []);

  const handleApiKeyChange = (provider: string, value: string) => {
    const newApiKeys = {
      ...apiKeys,
      [provider]: value
    };
    setApiKeys(newApiKeys);

    // Save settings immediately when API key changes
    const settings = {
      apiKeys: newApiKeys,
      folders: selectedFolders,
      chatMemory
    };
    localStorage.setItem('rohit_settings', JSON.stringify(settings));
  };

  const handleSaveSettings = () => {
    const settings = {
      apiKeys,
      folders: selectedFolders,
      chatMemory
    };
    localStorage.setItem('rohit_settings', JSON.stringify(settings));
  };

  const handleSelectFolder = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files[0]) {
      // Use the webkitRelativePath to get the folder path
      const folderPath = files[0].webkitRelativePath.split('/')[0];
      if (!selectedFolders.includes(folderPath)) {
        setSelectedFolders(prev => [...prev, folderPath]);
      }
    }
  };

  const removeFolder = (folder: string) => {
    setSelectedFolders(prev => prev.filter(f => f !== folder));
  };
  const handleChatMemoryChange = (value: string) => {
    const numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    // Use the utility function to set and validate the memory limit
    setMemoryLimit(numValue);
    setChatMemory(numValue);
  };

  const handleTestOllamaConnection = async () => {
    setIsTestingOllama(true);
    try {
      const status = await testOllamaConnection();
      setOllamaStatus(status);
    } catch (error) {
      console.error('Error testing Ollama connection:', error);
      setOllamaStatus({
        isConnected: false,
        error: 'Connection test failed',
        modelsCount: 0
      });
    } finally {
      setIsTestingOllama(false);
    }
  };

  const handleOllamaStatusChange = (status: OllamaStatus) => {
    setOllamaStatus(status);
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.header}>
          <Link to="/" className={styles.homeLink}>← Back to Home</Link>
          <h1>Settings</h1>
        </div>

        <div className={styles.settingsList}>
          <div className={styles.settingItem}>
            <div className={styles.memorySettingHeader}>
              <label htmlFor="chatMemory">Conversation Memory</label>
              <span className={styles.memoryValue}>{chatMemory} message{chatMemory !== 1 ? 's' : ''}</span>
            </div>
            <div className={styles.memoryDescription}>
              Controls how many recent messages the AI remembers from your conversation history.
              Higher values provide more context but may slow down responses in long conversations.
            </div>
            <div className={styles.memorySliderContainer}>
              <input
                type="range"
                id="chatMemory"
                min={1}
                max={30}
                value={chatMemory}
                onChange={(e) => handleChatMemoryChange(e.target.value)}
                className={styles.memorySlider}
              />
              <div className={styles.sliderLabels}>
                <span>1</span>
                <span>15</span>
                <span>30</span>
              </div>
            </div>
            <div className={styles.memoryInputContainer}>
              <input
                type="number"
                min={1}
                max={30}
                value={chatMemory}
                onChange={(e) => handleChatMemoryChange(e.target.value)}
                className={styles.memoryNumberInput}
                title="Enter exact number (1-30)"
              />
              <div className={styles.memoryPresets}>
                <button
                  onClick={() => handleChatMemoryChange('3')}
                  className={`${styles.presetButton} ${chatMemory === 3 ? styles.active : ''}`}
                >
                  Short (3)
                </button>
                <button
                  onClick={() => handleChatMemoryChange('10')}
                  className={`${styles.presetButton} ${chatMemory === 10 ? styles.active : ''}`}
                >
                  Medium (10)
                </button>
                <button
                  onClick={() => handleChatMemoryChange('20')}
                  className={`${styles.presetButton} ${chatMemory === 20 ? styles.active : ''}`}
                >
                  Long (20)
                </button>
              </div>
            </div>
            <div className={styles.memoryImpact}>
              <div className={styles.impactItem}>
                <span className={styles.impactLabel}>Context:</span>
                <span className={styles.impactValue}>
                  {getMemoryImpact(chatMemory).context}
                </span>
              </div>
              <div className={styles.impactItem}>
                <span className={styles.impactLabel}>Performance:</span>
                <span className={styles.impactValue}>
                  {getMemoryImpact(chatMemory).performance}
                </span>
              </div>
            </div>
            <div className={styles.memoryDescription} style={{ marginTop: '0.75rem', fontSize: '0.8rem', fontStyle: 'italic' }}>
              {getMemoryImpact(chatMemory).description}
            </div>
          </div>

          <div className={styles.settingItem}>
            <label>Folder Access</label>
            <div className={styles.folderControls}>
              <input
                ref={fileInputRef}
                type="file"
                webkitdirectory=""
                style={{ display: 'none' }}
                onChange={handleFileInputChange}
              />
              <button onClick={handleSelectFolder} className={styles.button}>
                Add Folder
              </button>
              <div className={styles.folderList}>
                {selectedFolders.map((folder, index) => (
                  <div key={index} className={styles.folderItem}>
                    <span>{folder}</span>
                    <button
                      onClick={() => removeFolder(folder)}
                      className={styles.removeButton}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className={styles.sectionTitle}>Model Providers</div>

          {Object.entries(PROVIDERS).map(([providerId, provider]) => (
            <div key={providerId} className={styles.settingItem}>
              <div className={styles.providerInfo}>
                <label htmlFor={`apiKey-${providerId}`}>{provider.name}</label>
                <div className={styles.providerDescription}>
                  {provider.description}
                  <a
                    href={provider.docsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.docsLink}
                  >
                    {providerId === 'ollama' ? 'Download Ollama →' : 'Get API Key →'}
                  </a>
                </div>
              </div>
              {providerId === 'ollama' ? (
                <div className={styles.ollamaContainer}>
                  <div className={styles.ollamaStatus}>
                    <span>Local service - no API key required</span>
                    <small>Make sure Ollama is running on http://localhost:11434</small>
                  </div>
                  <div className={styles.ollamaControls}>
                    <OllamaStatusIndicator
                      onStatusChange={handleOllamaStatusChange}
                      showDetails={true}
                    />
                    <button
                      onClick={handleTestOllamaConnection}
                      disabled={isTestingOllama}
                      className={styles.testButton}
                      title="Test Ollama connection"
                    >
                      {isTestingOllama ? 'Testing...' : 'Test Connection'}
                    </button>
                  </div>
                  {ollamaStatus.isConnected && ollamaStatus.modelsCount === 0 && (
                    <div className={styles.ollamaWarning}>
                      ⚠️ Ollama is running but no models are installed.
                      <br />
                      Install models using: <code>ollama pull llama2</code>
                    </div>
                  )}
                </div>
              ) : (
                <input
                  type="password"
                  id={`apiKey-${providerId}`}
                  value={apiKeys[providerId] || ''}
                  onChange={(e) => handleApiKeyChange(providerId, e.target.value)}
                  placeholder={`Enter ${provider.name} API key`}
                />
              )}
            </div>
          ))}

          <div className={styles.settingItem}>
            <button onClick={handleSaveSettings} className={styles.saveButton}>
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;