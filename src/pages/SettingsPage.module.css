.container {
  min-height: 100vh;
  background-color: var(--background-primary);
  color: var(--text-primary);
  padding: 2rem;
}

.content {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 500;
}

.homeLink {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: var(--background-tertiary);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.homeLink:hover {
  color: var(--text-primary);
  background-color: var(--background-quaternary);
}

.settingsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.settingItem {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--background-tertiary);
  border-radius: 6px;
  gap: 1rem;
}

.settingItem label {
  color: var(--text-primary);
  font-weight: 500;
}

.settingItem input {
  flex: 1;
  padding: 0.5rem;
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 1rem;
}

.settingItem input:focus {
  outline: none;
  border-color: var(--border-focus);
}

.sectionTitle {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--text-primary);
}

.providerInfo {
  flex: 0 0 200px;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.providerDescription {
  font-size: 0.875rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.docsLink {
  color: var(--accent-coral);
  text-decoration: none;
  font-size: 0.75rem;
  white-space: nowrap;
}

.docsLink:hover {
  text-decoration: underline;
}

.folderControls {
  flex: 1;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.button {
  padding: 0.5rem 1rem;
  background-color: var(--background-quaternary);
  border: none;
  border-radius: 4px;
  color: var(--text-primary);
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.button:hover {
  background-color: var(--background-quinary);
}

.folderList {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.folderItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  font-size: 0.9rem;
}

.removeButton {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0 0.5rem;
  font-size: 1.2rem;
  line-height: 1;
}

.removeButton:hover {
  color: var(--text-primary);
}

.saveButton {
  padding: 0.75rem 1.5rem;
  background-color: var(--accent-coral);
  border: none;
  border-radius: 6px;
  color: var(--background-primary);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settingGroup {
  margin-bottom: 2rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--settings-group-bg, #f5f5f5);
}

.inputGroup {
  margin: 1rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.numberInput {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid var(--input-border-color, #ccc);
  border-radius: 4px;
  font-size: 1rem;
}

.hint {
  font-size: 0.875rem;
  color: var(--hint-text-color, #666);
  margin-top: 0.5rem;
}

.inputWithValidation {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.validationHint {
  font-size: 0.75rem;
  color: var(--error-color, #dc2626);
  min-height: 1rem;
}

.settingItem input[type="number"] {
  width: 80px;
  padding: 8px;
  border: 1px solid var(--input-border-color, #ccc);
  border-radius: 4px;
  font-size: 14px;
}

.settingItem input[type="number"]:invalid {
  border-color: var(--error-color, #dc2626);
}

/* Enhanced Memory Settings Styles */
.memorySettingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0.5rem;
}

.memoryValue {
  font-size: 0.9rem;
  color: var(--accent-coral);
  font-weight: 600;
  background-color: var(--background-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.memoryDescription {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 1rem;
}

.memorySliderContainer {
  margin-bottom: 1rem;
}

.memorySlider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--background-primary);
  outline: none;
  -webkit-appearance: none;
  margin-bottom: 0.5rem;
}

.memorySlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-coral);
  cursor: pointer;
  border: 2px solid var(--background-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.memorySlider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-coral);
  cursor: pointer;
  border: 2px solid var(--background-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sliderLabels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.memoryInputContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.memoryNumberInput {
  width: 80px !important;
  padding: 0.5rem !important;
  text-align: center;
  font-weight: 500;
}

.memoryPresets {
  display: flex;
  gap: 0.5rem;
}

.presetButton {
  padding: 0.5rem 0.75rem;
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.presetButton:hover {
  background-color: var(--background-quaternary);
  color: var(--text-primary);
}

.presetButton.active {
  background-color: var(--accent-coral);
  color: var(--background-primary);
  border-color: var(--accent-coral);
}

.memoryImpact {
  display: flex;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.impactItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.impactLabel {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.impactValue {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Override for memory setting item to be vertical */
.settingItem:has(.memorySettingHeader) {
  flex-direction: column;
  align-items: stretch;
  padding: 1.5rem;
}

/* Ollama status styles */
.ollamaContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ollamaStatus {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.ollamaStatus span {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.ollamaStatus small {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.ollamaControls {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--background-primary);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.testButton {
  padding: 0.5rem 1rem;
  background-color: var(--accent-coral);
  border: none;
  border-radius: 4px;
  color: var(--background-primary);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.testButton:hover:not(:disabled) {
  background-color: var(--accent-coral-dark, #e55a4f);
  transform: translateY(-1px);
}

.testButton:disabled {
  background-color: var(--text-secondary);
  cursor: not-allowed;
  transform: none;
}

.ollamaWarning {
  padding: 0.75rem;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 4px;
  color: var(--warning-color, #856404);
  font-size: 0.875rem;
  line-height: 1.4;
}

.ollamaWarning code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
}